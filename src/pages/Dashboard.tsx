import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Key, 
  Copy, 
  Eye, 
  EyeOff, 
  RefreshCw, 
  Activity, 
  Volume2, 
  FileText, 
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { useAuth } from '../contexts/AuthContext';
import { ApiKeyService } from '../services/apiKeyService';
import { UsageService } from '../services/usageService';
import { ActivityService, type ActivityLog } from '../services/activityService';

export const Dashboard: React.FC = () => {
  const { user, profile } = useAuth();
  const [showApiKey, setShowApiKey] = React.useState(false);
  const [copied, setCopied] = React.useState(false);
  const [apiKeys, setApiKeys] = React.useState([]);
  const [usage, setUsage] = React.useState({ used: 0, total: 10000 });
  const [loading, setLoading] = React.useState(true);
  const [recentActivities, setRecentActivities] = React.useState<ActivityLog[]>([]);
  const [activitiesLoading, setActivitiesLoading] = React.useState(true);

  React.useEffect(() => {
    if (user) {
      loadDashboardData();
      loadRecentActivities();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // 加载 API 密钥
      const keys = await ApiKeyService.getApiKeys(user!.id);
      setApiKeys(keys);

      // 加载用量统计
      const usageStats = await UsageService.getCurrentMonthUsage(user!.id);
      setUsage({ used: usageStats.totalCharacters, total: 10000 });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentActivities = async () => {
    try {
      setActivitiesLoading(true);
      const activities = await ActivityService.getRecentActivities(user!.id, 5);
      setRecentActivities(activities);
    } catch (error) {
      console.error('Error loading recent activities:', error);
    } finally {
      setActivitiesLoading(false);
    }
  };

  const usagePercent = (usage.used / usage.total) * 100;
  const primaryApiKey = apiKeys[0];

  const handleCopy = () => {
    if (primaryApiKey && !primaryApiKey.key_prefix.includes('...')) {
      navigator.clipboard.writeText(primaryApiKey.key_prefix);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const getActivityIcon = (activity: ActivityLog) => {
    switch (activity.activity_type) {
      case 'api_call':
        return activity.action === 'success' ? 'bg-green-500' : 'bg-red-500';
      case 'api_key':
        return 'bg-blue-500';
      case 'voice_model':
        return 'bg-purple-500';
      case 'account':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold mb-2">欢迎回来，{profile?.name || user?.email}！</h1>
            <p className="text-gray-400">准备好创造一些令人惊叹的语音体验了吗？</p>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* API Key Card */}
            <Card glass glow className="col-span-full lg:col-span-2">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Key className="h-5 w-5 text-purple-500" />
                  <h3 className="text-lg font-semibold">API 密钥</h3>
                </div>
                <Button variant="ghost" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重新生成
                </Button>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <code className="flex-1 bg-gray-800 p-3 rounded-lg font-mono text-sm">
                    {primaryApiKey ? (
                      showApiKey ? (
                        primaryApiKey.key_prefix.includes('...')
                          ? primaryApiKey.key_prefix + ' (不完整，请重新生成)'
                          : primaryApiKey.key_prefix
                      ) : '••••••••••••••••••••••••••••••••'
                    ) : (
                      loading ? '加载中...' : '暂无 API 密钥'
                    )}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                    disabled={!primaryApiKey}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    disabled={!primaryApiKey || (primaryApiKey && primaryApiKey.key_prefix.includes('...'))}
                    className={copied ? 'text-green-400' : ''}
                    title={primaryApiKey && primaryApiKey.key_prefix.includes('...') ? '请重新生成完整密钥' : '复制密钥'}
                  >
                    {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
                
                <p className="text-sm text-gray-400">
                  {primaryApiKey 
                    ? '请妥善保管您的 API 密钥，不要在客户端代码中暴露'
                    : '您还没有创建 API 密钥，请前往 API 密钥页面创建'}
                </p>
              </div>
            </Card>

            {/* Usage Dashboard */}
            <Card glass glow>
              <div className="flex items-center space-x-2 mb-4">
                <Activity className="h-5 w-5 text-blue-500" />
                <h3 className="text-lg font-semibold">本月用量</h3>
              </div>
              
              <div className="space-y-4">
                <div className="relative">
                  <div className="flex justify-center items-center h-24">
                    <div className="relative w-20 h-20">
                      <svg className="transform -rotate-90 w-full h-full" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeDasharray="0, 100"
                          className="text-gray-700"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeDasharray={`${usagePercent}, 100`}
                          className="text-purple-500"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-lg font-bold">{Math.round(usagePercent)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="text-center">
                  <p className="text-sm text-gray-400">
                    {usage.used.toLocaleString()} / {usage.total.toLocaleString()} 字符
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    还剩 {(usage.total - usage.used).toLocaleString()} 字符
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card glass className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Volume2 className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">声音实验室</h3>
                  <p className="text-sm text-gray-400">体验我们的语音合成技术</p>
                </div>
              </div>
              <Link to="/voice-lab">
                <Button variant="primary" className="w-full" glow>
                  前往声音实验室
                </Button>
              </Link>
            </Card>

            <Card glass className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-500" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">快速入门</h3>
                  <p className="text-sm text-gray-400">3分钟学会如何使用 API</p>
                </div>
              </div>
              <Link to="/docs">
                <Button variant="glass" className="w-full">
                  阅读文档
                </Button>
              </Link>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card glass>
            <div className="flex items-center space-x-2 mb-6">
              <Activity className="h-5 w-5 text-green-500" />
              <h3 className="text-lg font-semibold">最近活动</h3>
            </div>
            
            <div className="space-y-4">
              {activitiesLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500 mx-auto mb-2"></div>
                  <p className="text-gray-400 text-sm">加载活动记录...</p>
                </div>
              ) : recentActivities.length === 0 ? (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-gray-600 mx-auto mb-3" />
                  <p className="text-gray-400">暂无活动记录</p>
                  <p className="text-gray-500 text-sm mt-1">开始使用 SoulVoice 后，您的活动将显示在这里</p>
                </div>
              ) : (
                recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 py-2">
                    <div className={`w-2 h-2 rounded-full ${getActivityIcon(activity)}`} />
                    <div className="flex-1">
                      <p className="text-sm">{activity.message}</p>
                      <p className="text-xs text-gray-500">
                        {ActivityService.formatRelativeTime(activity.created_at)}
                      </p>
                    </div>
                    {activity.metadata?.characters && (
                      <div className="text-xs text-gray-400">
                        {activity.metadata.characters} 字符
                      </div>
                    )}
                    {activity.activity_type === 'api_call' && activity.action === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : activity.activity_type === 'api_call' && activity.action === 'error' ? (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                    )}
                  </div>
                ))
              )}
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};